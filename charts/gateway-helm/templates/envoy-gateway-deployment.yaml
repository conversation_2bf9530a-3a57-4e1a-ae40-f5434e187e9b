apiVersion: apps/v1
kind: Deployment
metadata:
  name: envoy-gateway
  namespace: '{{ .Release.Namespace }}'
  {{- with .Values.deployment.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    control-plane: envoy-gateway
  {{- include "eg.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.deployment.replicas }}
  selector:
    matchLabels:
      control-plane: envoy-gateway
    {{- include "eg.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.deployment.pod.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        control-plane: envoy-gateway
      {{- include "eg.selectorLabels" . | nindent 8 }}
      {{- with .Values.deployment.pod.labels }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- with .Values.deployment.pod.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.deployment.pod.nodeSelector }}
      nodeSelector:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.deployment.pod.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 6 }}
      {{- end }}
      {{- with .Values.deployment.pod.tolerations }}
      tolerations:
        {{- toYaml . | nindent 6 }}
      {{- end }}
      containers:
      - args:
        - server
        - --config-path=/config/envoy-gateway.yaml
        env:
        - name: ENVOY_GATEWAY_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: KUBERNETES_CLUSTER_DOMAIN
          value: {{ .Values.kubernetesClusterDomain }}
        image: {{ include "eg.image" . }}
        imagePullPolicy: {{ include "eg.image.pullPolicy" . }}
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        name: envoy-gateway
        ports:
        {{- range .Values.deployment.ports }}
        - containerPort: {{ .port }}
          name: {{ .name }}
        {{- end}}
        {{- if .Values.topologyInjector.enabled }}
        - name: webhook
          containerPort: 9443
        {{- end }}
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          {{- toYaml .Values.deployment.envoyGateway.resources | nindent 10 }}
        securityContext:
          {{- toYaml .Values.deployment.envoyGateway.securityContext | nindent 10 }}
        volumeMounts:
        - mountPath: /config
          name: envoy-gateway-config
          readOnly: true
        - mountPath: /certs
          name: certs
          readOnly: true
      {{- include "eg.image.pullSecrets" . | nindent 6 }}
      {{- with .Values.deployment.priorityClassName }}
      priorityClassName: {{ . | quote }}
      {{- end }}
      serviceAccountName: envoy-gateway
      terminationGracePeriodSeconds: 10
      volumes:
      - configMap:
          defaultMode: 420
          name: envoy-gateway-config
        name: envoy-gateway-config
      - name: certs
        secret:
          secretName: envoy-gateway
